import React, { useState, useEffect } from 'react';
import { Box, Typography, styled, useTheme, useMediaQuery } from '@mui/material';
import { motion, AnimatePresence } from 'framer-motion';
import { FaLinkedinIn, FaTwitter, FaInstagram, FaFacebookF } from 'react-icons/fa';

const teamMembers = [
  {
    name: '<PERSON>',
    role: 'CEO & Founder',
    tagline: 'Leadership • Vision • 10+ Years',
    description: 'Drives strategic growth with over a decade of leadership experience in consulting.',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
    linkedin: 'https://www.linkedin.com/in/janedoe',
    twitter: 'https://twitter.com/janedoe',
    instagram: 'https://www.instagram.com/janedoe/',
    facebook: 'https://www.facebook.com/janedoe',
  },
  {
    name: '<PERSON>',
    role: 'Lead Developer',
    tagline: 'Innovation • Coding • 8+ Years',
    description: 'Expert in building scalable tech solutions with a focus on modern development practices.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
    linkedin: 'https://www.linkedin.com/in/johnsmith',
    twitter: 'https://twitter.com/johnsmith',
    instagram: 'https://www.instagram.com/johnsmith/',
    facebook: 'https://www.facebook.com/johnsmith',
  },
  {
    name: 'Emily Johnson',
    role: 'Marketing Director',
    tagline: 'Strategy • Branding • 6+ Years',
    description: 'Leads creative campaigns to elevate brand presence across multiple platforms.',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
    linkedin: 'https://www.linkedin.com/in/emilyjohnson',
    twitter: 'https://twitter.com/emilyjohnson',
    instagram: 'https://www.instagram.com/emilyjohnson/',
    facebook: 'https://www.facebook.com/emilyjohnson',
  },
  {
    name: 'Michael Brown',
    role: 'Design Lead',
    tagline: 'Creativity • UI/UX • 5+ Years',
    description: 'Crafts stunning user interfaces with a passion for user-centered design.',
    image: 'https://images.unsplash.com/photo-1500048993953-d23a436266cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
    linkedin: 'https://www.linkedin.com/in/michaelbrown',
    twitter: 'https://twitter.com/michaelbrown',
    instagram: 'https://www.instagram.com/michaelbrown/',
    facebook: 'https://www.facebook.com/michaelbrown',
  },
];

const Section = styled(Box)(({ theme }) => ({
  marginTop: '60px',
  padding: '80px 24px 60px',
  background: "linear-gradient(135deg, #fafafa 0%, #c8c9d3	 100%)",
  textAlign: 'center',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  width: '100%',
  maxWidth: '100vw',
  boxSizing: 'border-box',
}));

const Grid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: '32px',
  marginTop: '48px',
  padding: '0 12px',
  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
  width: '100%',
  maxWidth: '1440px',
  marginLeft: 'auto',
  marginRight: 'auto',
  boxSizing: 'border-box',
}));

const CardWrapper = styled(Box)(({ theme }) => ({
  perspective: '2000px',
  width: '100%',
  maxWidth: '380px',
  height: '560px',
  margin: '0 auto',
  position: 'relative',
  [theme.breakpoints.down('sm')]: {
    maxWidth: '95%',
    height: '540px',
    padding: '0 8px',
  },
}));

const Card = styled(motion.div)(() => ({
  width: '100%',
  height: '100%',
  borderRadius: '20px',
  background: 'rgba(231, 236, 255, 0.3)',
  border: '2px solid rgba(231, 236, 255, 0.5)',
  boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2), 0 10px 25px rgba(0, 0, 0, 0.15)',
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
  '&:hover': {
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3), 0 15px 40px rgba(0, 0, 0, 0.2)',
    scale: 1.05,
    '& .card-content': {
      transform: 'translateY(-10px)',
    },
  },
}));

const CardContent = styled(motion.div)(() => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  alignItems: 'center',
  textAlign: 'center',
  padding: '20px',
  color: '#1e3c72',
  transition: 'transform 0.4s ease',
  zIndex: 1,
}));

const Image = styled('img')(({ theme }) => ({
  width: '100%',
  height: '280px',
  objectFit: 'cover',
  borderRadius: '15px 15px 0 0',
  marginBottom: '10px',
  objectPosition: 'center',
  [theme.breakpoints.down('sm')]: {
    height: '260px',
  },
}));

const SocialIcons = styled(motion.div)(() => ({
  display: 'flex',
  justifyContent: 'center',
  gap: '20px',
  padding: '15px 0',
  width: '100%',
  boxSizing: 'border-box',
}));

const OurTeam = () => {
  const [visibleIndex, setVisibleIndex] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    if (!isMobile) return;
    const interval = setInterval(() => {
      setVisibleIndex((prev) => (prev + 1) % teamMembers.length);
    }, 4000);
    return () => clearInterval(interval);
  }, [isMobile]);

  const cardVariants = {
    hidden: (index) => ({
      opacity: 0,
      x: index % 2 === 0 ? -200 : 200,
      y: Math.floor(index / 2) % 2 === 0 ? 150 : -150,
      rotate: index % 4 === 0 ? 10 : index % 4 === 2 ? -10 : 0,
    }),
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      rotate: 0,
      transition: { duration: 1.2, ease: [0.43, 0.13, 0.23, 0.96] },
    },
  };

  const iconVariants = {
    initial: { opacity: 0 },
    animate: {
      opacity: 1,
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: 'reverse',
      },
    },
  };

  return (
    <Section id="our-team">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1.2, ease: 'easeOut' }}
        viewport={{ once: true }}
      >
        <Typography variant="h3" sx={{ fontWeight: '900', mb: 3, color: '#1e3c72', textShadow: '2px 2px 4px rgba(0, 0, 0, 0.2)' }}>
          Our Amazing Team
        </Typography>
        <Typography variant="h6" sx={{ maxWidth: 700, mx: 'auto', mb: 4, color: '#2a5298', fontStyle: 'italic' }}>
          Discover the extraordinary talents shaping our future with passion and brilliance.
        </Typography>
      </motion.div>

      {isMobile ? (
        <Box sx={{ mt: 4, width: '100%' }}>
          <AnimatePresence mode="wait">
            <motion.div
              key={visibleIndex}
              initial={{ x: 200, opacity: 0, rotate: 15 }}
              animate={{ x: 0, opacity: 1, rotate: 0 }}
              exit={{ x: -200, opacity: 0, rotate: -15 }}
              transition={{ duration: 0.8, ease: 'easeInOut' }}
            >
              <CardWrapper>
                <Card>
                  <CardContent className="card-content">
                    <Image src={teamMembers[visibleIndex].image} alt={teamMembers[visibleIndex].name} />
                    <Typography variant="subtitle1" sx={{ opacity: 0.8, fontSize: '0.95rem', color: '#1e3c72' }}>
                      {teamMembers[visibleIndex].tagline}
                    </Typography>
                    <Typography variant="h5" sx={{ fontWeight: '700', mt: 1, color: '#2a5298' }}>
                      {teamMembers[visibleIndex].name}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1, fontSize: '0.9rem', color: '#1e3c72', minHeight: '100px' }}>
                      {teamMembers[visibleIndex].description}
                    </Typography>
                    <SocialIcons className="social-icons" variants={iconVariants} initial="initial" animate="animate">
                      <a href={teamMembers[visibleIndex].linkedin} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-200">
                        <FaLinkedinIn size={22} />
                      </a>
                      <a href={teamMembers[visibleIndex].twitter} target="_blank" rel="noopener noreferrer" className="text-sky-300 hover:text-sky-100">
                        <FaTwitter size={22} />
                      </a>
                      <a href={teamMembers[visibleIndex].instagram} target="_blank" rel="noopener noreferrer" className="text-pink-400 hover:text-pink-200">
                        <FaInstagram size={22} />
                      </a>
                      <a href={teamMembers[visibleIndex].facebook} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-300">
                        <FaFacebookF size={22} />
                      </a>
                    </SocialIcons>
                  </CardContent>
                </Card>
              </CardWrapper>
            </motion.div>
          </AnimatePresence>
        </Box>
      ) : (
        <Grid>
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              custom={index}
              initial="hidden"
              whileInView="visible"
              variants={cardVariants}
              viewport={{ once: true, amount: 0.2 }}
            >
              <CardWrapper>
                <Card>
                  <CardContent className="card-content">
                    <Image src={member.image} alt={member.name} />
                    <Typography variant="subtitle1" sx={{ opacity: 0.8, fontSize: '0.95rem', color: '#1e3c72' }}>
                      {member.tagline}
                    </Typography>
                    <Typography variant="h5" sx={{ fontWeight: '700', mt: 1, color: '#2a5298' }}>
                      {member.name}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1, fontSize: '0.9rem', color: '#1e3c72', minHeight: '100px' }}>
                      {member.description}
                    </Typography>
                    <SocialIcons className="social-icons" variants={iconVariants} initial="initial" animate="animate">
                      <a href={member.linkedin} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-200">
                        <FaLinkedinIn size={22} />
                      </a>
                      <a href={member.twitter} target="_blank" rel="noopener noreferrer" className="text-sky-300 hover:text-sky-100">
                        <FaTwitter size={22} />
                      </a>
                      <a href={member.instagram} target="_blank" rel="noopener noreferrer" className="text-pink-400 hover:text-pink-200">
                        <FaInstagram size={22} />
                      </a>
                      <a href={member.facebook} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-300">
                        <FaFacebookF size={22} />
                      </a>
                    </SocialIcons>
                  </CardContent>
                </Card>
              </CardWrapper>
            </motion.div>
          ))}
        </Grid>
      )}
    </Section>
  );
};

export default OurTeam;
