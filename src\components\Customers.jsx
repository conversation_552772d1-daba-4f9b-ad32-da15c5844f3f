import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, styled, Container } from '@mui/material';
import { motion, AnimatePresence, useInView } from 'framer-motion';
import { useTheme, useMediaQuery } from '@mui/material';
import { AutoAwesome, TrendingUp, People } from '@mui/icons-material';

const testimonials = [
  {
    name: '<PERSON>',
    role: 'Entrepreneur',
    text: 'The team transformed my business with their innovative solutions. Highly recommend their services!',
    rating: '★★★★★',
  },
  {
    name: '<PERSON>',
    role: 'Tech Lead',
    text: 'Amazing support and cutting-edge technology. My project was completed ahead of schedule.',
    rating: '★★★★☆',
  },
  {
    name: '<PERSON>',
    role: 'Marketing Manager',
    text: 'Their creative campaigns doubled our engagement. Truly a game-changer!',
    rating: '★★★★★',
  },
  {
    name: '<PERSON>',
    role: 'Startup Founder',
    text: 'Exceptional design and development work. My app looks and performs flawlessly.',
    rating: '★★★★☆',
  },
];

const Section = styled(Box)(({ theme }) => ({
  padding: '80px 16px',
  background: 'linear-gradient(135deg, #fafafa 0%, #e7ecff 100%)',
  fontFamily: 'Roboto, sans-serif',
  textAlign: 'center',
}));

const GridContainer = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fit, minmax(260px, 1fr))',
  gap: '32px',
  marginTop: '48px',
  padding: '0 12px',
  [theme.breakpoints.down('sm')]: {
    display: 'block',
    overflow: 'hidden',
    width: '100%',
    padding: 0,
  },
}));

const Card = styled(motion.div)(({ theme }) => ({
  background: 'rgba(231, 236, 255, 0.25)',
  border: '1.5px solid rgba(231, 236, 255, 0.45)',
  borderRadius: '20px',
  padding: '24px 20px',
  backdropFilter: 'blur(14px)',
  color: '#1e3c72',
  boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
  transition: 'transform 0.4s ease, box-shadow 0.4s ease',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: '0 18px 48px rgba(0, 0, 0, 0.12)',
  },
  [theme.breakpoints.down('sm')]: {
    width: '100%',
    maxWidth: '100%',
    height: 'auto',
    margin: '0 auto',
  },
}));

const Customers = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isPaused, setIsPaused] = useState(false);

  useEffect(() => {
    let interval;
    if (!isPaused && isMobile) {
      interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % testimonials.length);
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [isPaused, isMobile]);

  const slideVariants = {
    enter: { x: 400, opacity: 0, scale: 0.95 },
    center: { x: 0, opacity: 1, scale: 1, transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1] } },
    exit: { x: -400, opacity: 0, scale: 0.95, transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1] } },
  };

  return (
    <Section id="customers">
      <motion.div
        initial={{ opacity: 0, x: 100 }}
        whileInView={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        <Typography variant="h3" sx={{ fontWeight: '900', mb: 3, color: '#1e3c72', textShadow: '2px 2px 4px rgba(0, 0, 0, 0.2)' }}>
        
          What Our Customers Say
        </Typography>
        <Typography
          variant="h6"
          sx={{
            maxWidth: 700,
            mx: 'auto',
            mb: 4,
            color: '#2a5298',
            fontStyle: 'italic',
          }}
        >
          Hear from our satisfied clients about their experiences with us.
        </Typography>
      </motion.div>

      <GridContainer>
        {!isMobile ? (
          testimonials.map((item, i) => (
            <Card
              key={i}
              initial={{ opacity: 0, x: 60 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: i * 0.15 }}
              viewport={{ once: true }}
            >
              <Typography variant="body1" sx={{ fontSize: '1.05rem', mb: 2, fontStyle: 'italic' }}>
                “{item.text}”
              </Typography>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#2a5298', mb: 1 }}>
                {item.name} – {item.role}
              </Typography>
              <Typography variant="body2" sx={{ color: '#1e3c72', opacity: 0.9 }}>
                {item.rating}
              </Typography>
            </Card>
          ))
        ) : (
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              custom={0}
              initial="enter"
              animate="center"
              exit="exit"
              variants={slideVariants}
              transition={{ duration: 0.9, ease: [0.25, 0.1, 0.25, 1] }}
              style={{ position: 'relative', width: '100%' }}
            >
              <Card>
                <Typography variant="body1" sx={{ fontSize: '1.05rem', mb: 2, fontStyle: 'italic' }}>
                  “{testimonials[currentIndex].text}”
                </Typography>
                <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#2a5298', mb: 1 }}>
                  {testimonials[currentIndex].name} – {testimonials[currentIndex].role}
                </Typography>
                <Typography variant="body2" sx={{ color: '#1e3c72', opacity: 0.9 }}>
                  {testimonials[currentIndex].rating}
                </Typography>
              </Card>
            </motion.div>
          </AnimatePresence>
        )}
      </GridContainer>
    </Section>
  );
};

export default Customers;