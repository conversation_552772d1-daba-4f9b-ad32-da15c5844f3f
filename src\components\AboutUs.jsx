import React from 'react';
import { Box, Typography, styled } from '@mui/material';
import { motion } from 'framer-motion';
// import aboutImage from '../assets/images/about-nelainey.jpg'; 

const Section = styled(Box)(({ theme }) => ({
  padding: '80px 20px',
  background: 'linear-gradient(135deg, #eaf1ff, #f9fafc)',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  textAlign: 'center',
  position: 'relative',
  overflow: 'hidden',
  backdropFilter: 'blur(5px)',
  WebkitBackdropFilter: 'blur(5px)',
  [theme.breakpoints.up('md')]: {
    padding: '100px 60px',
  },
}));

const Image = styled('img')(({ theme }) => ({
  maxWidth: '100%',
  borderRadius: '24px',
  marginBottom: '40px',
  boxShadow: '0 12px 30px rgba(0, 0, 0, 0.1)',
  backdropFilter: 'blur(10px)',
  WebkitBackdropFilter: 'blur(10px)',
  background: 'rgba(255, 255, 255, 0.2)',
  [theme.breakpoints.up('md')]: {
    maxWidth: '600px',
  },
}));

const Content = styled(Box)(({ theme }) => ({
  maxWidth: '1200px', // Increased width for a wider layout
  color: '#1a237e',
  fontFamily: "'Roboto', sans-serif",
  background: 'rgba(255, 255, 255, 0.15)',
  backdropFilter: 'blur(25px)',
  WebkitBackdropFilter: 'blur(15px)',
  borderRadius: '20px',
  padding: '80px 60px',
  boxShadow: '0 16px 40px rgba(0, 0, 0, 0.1)',
  [theme.breakpoints.up('md')]: {
    padding: '50px 60px',
  },
}));

const Highlight = styled('span')({
  color: '#2a5298',
  fontWeight: 700,
});

const AboutUsSection = () => {
  return (
    <Section id="about">
      <motion.div
        initial={{ opacity: 0, y: 80 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        {/* <Image src={aboutImage} alt="About Nelainey Consulting" /> */}
        <Content>
          <Typography variant="h3" sx={{ fontWeight: 'bold', mb: 3, fontFamily: 'Montserrat, sans-serif' }}>
            About <Highlight>Nelainey Consulting</Highlight>
          </Typography>
          <Typography variant="h6" sx={{ fontWeight: 400, color: '#333', lineHeight: 1.6 }}>
            Welcome to <Highlight>Nelainey Consulting</Highlight> – a premier Tanzania-based professional consulting firm
            dedicated to transforming businesses with tailored expertise. Established with a vision to empower growth,
            we offer a comprehensive suite of services including <Highlight>Accounting</Highlight>, <Highlight>Auditing</Highlight>,
            <Highlight>Tax Planning</Highlight>, <Highlight>ICT Solutions</Highlight>, <Highlight>Business Strategy</Highlight>,
            and <Highlight>Corporate Training</Highlight>. Our mission is to provide innovative, reliable, and sustainable
            solutions that drive success across diverse industries.
          </Typography>
          <Typography variant="body1" sx={{ mt: 3, color: '#555', fontSize: '1rem', lineHeight: 1.8 }}>
            Since our founding in 2010, Nelainey Consulting has served over 500 clients across Tanzania, East Africa, and
            beyond, helping startups build financial foundations and multinational corporations navigate complex regulatory
            landscapes. Our team of certified professionals brings decades of experience, leveraging cutting-edge technology
            and strategic insights to ensure resilience, compliance, and innovation. Whether you need expert tax optimization,
            robust ICT infrastructure, or leadership development programs, we craft bespoke strategies to meet your unique
            needs. Join us on a journey to elevate your business to new heights!
          </Typography>
        </Content>
      </motion.div>
    </Section>
  );
};

export default AboutUsSection;