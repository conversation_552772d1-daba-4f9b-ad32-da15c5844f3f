import React, { useState, useRef } from "react";
import { Box, styled, Container } from "@mui/material";
import { motion, useInView } from "framer-motion";
import { Typography } from "@mui/material";
import {
  AutoAwesome,
  TrendingUp,
  AccountBalance,
  Assessment,
  Security,
  Computer,
} from "@mui/icons-material";

import img1 from "../assets/images/blog-1.jpg";
import img2 from "../assets/images/blog-2.jpg";
import img3 from "../assets/images/blog-3.jpg";
import img4 from "../assets/images/blog-4.jpg";
import img5 from "../assets/images/blog-5.jpg";

const FEATURES = [
  {
    img: img5,
    icon: AccountBalance,
    title: "Financial Services",
    tagline: "Financial Planning",
    color: "#3b82f6",
    desc:
      "We provide comprehensive financial planning, budgeting, and investment advice to ensure your business thrives financially, tailored to your specific needs across Tanzania and globally.",
  },
  {
    img: img2,
    icon: Assessment,
    title: "Accounting Solutions",
    tagline: "Bookkeeping & Reporting",
    color: "#10b981",
    desc:
      "Our expert accounting services include bookkeeping, financial reporting, and audits, delivering accuracy and compliance to support your business growth and decision-making.",
  },
  {
    img: img3,
    icon: TrendingUp,
    title: "Tax Consulting",
    tagline: "Tax Optimization",
    color: "#f59e0b",
    desc:
      "We offer strategic tax planning and compliance services to optimize your tax obligations, ensuring you benefit from the latest regulations and minimize liabilities effectively.",
  },
  {
    img: img4,
    icon: Security,
    title: "Assurance & ICT",
    tagline: "Assurance & Technology",
    color: "#ef4444",
    desc:
      "Our assurance services guarantee reliability in financial statements, while our ICT solutions enhance your business operations with cutting-edge technology and cybersecurity measures.",
  },
];

const Section = styled(Box)(({ theme }) => ({
  marginTop: '80px',
  padding: '80px 0',
  background: `
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)
  `,
  position: 'relative',
  overflow: 'hidden',
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      )
    `,
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('md')]: {
    padding: '60px 0',
    marginTop: '60px',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '40px 0',
    marginTop: '40px',
  },
}));

const FeaturesGrid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: '32px',
  marginTop: '60px',
  padding: '0 20px',
  gridTemplateColumns: 'repeat(4, 1fr)', // Fixed 4 columns for desktop
  width: '100%',
  maxWidth: '1400px',
  marginLeft: 'auto',
  marginRight: 'auto',
  position: 'relative',
  justifyItems: 'center',
  [theme.breakpoints.up('xl')]: {
    gridTemplateColumns: 'repeat(4, 1fr)', // Keep 4 columns on extra large screens
    gap: '40px',
    maxWidth: '1600px',
  },
  [theme.breakpoints.down('lg')]: {
    gridTemplateColumns: 'repeat(2, 1fr)', // 2 columns for large tablets
    gap: '28px',
  },
  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: 'repeat(2, 1fr)', // 2 columns for tablets
    gap: '24px',
    padding: '0 16px',
  },
  [theme.breakpoints.down('sm')]: {
    gridTemplateColumns: '1fr', // Single column for mobile
    gap: '20px',
    padding: '0 12px',
  },
}));

const FeatureCard = styled(motion.div)(({ theme }) => ({
  position: 'relative',
  width: '100%',
  maxWidth: '380px',
  height: '480px',
  borderRadius: '24px',
  overflow: 'hidden',
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'flex-end',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  willChange: 'transform, box-shadow',
  boxShadow: `
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: `
      0 35px 70px -12px rgba(0, 0, 0, 0.35),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 50px rgba(59, 130, 246, 0.3)
    `,
  },
  '&:active': {
    transform: 'translateY(-4px) scale(0.98)',
  },
  [theme.breakpoints.down('lg')]: {
    maxWidth: '360px',
    height: '460px',
  },
  [theme.breakpoints.down('md')]: {
    maxWidth: '340px',
    height: '440px',
  },
  [theme.breakpoints.down('sm')]: {
    maxWidth: '100%',
    height: '400px',
    borderRadius: '20px',
  },
}));

const Overlay = styled("div")(({ theme }) => ({
  position: "absolute",
  inset: 0,
  background: `
    linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.1) 0%,
      rgba(0, 0, 0, 0.3) 50%,
      rgba(0, 0, 0, 0.8) 100%
    )
  `,
  backdropFilter: "blur(2px)",
  WebkitBackdropFilter: "blur(2px)",
  zIndex: 1,
  transition: "all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)",
}));

const CardContent = styled("div")(({ theme }) => ({
  position: "relative",
  zIndex: 2,
  color: "#f8fafc",
  textAlign: "left",
  padding: "32px 24px",
  width: "100%",
  background: `
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.15) 0%,
      rgba(255, 255, 255, 0.05) 100%
    )
  `,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(20px)',
  WebkitBackdropFilter: 'blur(20px)',
  borderRadius: '20px 20px 24px 24px',
  [theme.breakpoints.down('sm')]: {
    padding: '24px 20px',
  },
}));

const ReadMoreButton = styled(motion.button)(({ theme }) => ({
  marginTop: "16px",
  padding: "12px 24px",
  fontSize: "0.875rem",
  fontWeight: "700",
  borderRadius: "50px",
  color: "#ffffff",
  background: `linear-gradient(135deg,
    #3b82f6 0%,
    #2563eb 50%,
    #1d4ed8 100%
  )`,
  border: "none",
  transition: "all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)",
  cursor: "pointer",
  boxShadow: "0 8px 25px rgba(59, 130, 246, 0.4)",
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent)',
    transition: 'left 0.6s ease',
  },
  "&:hover": {
    transform: "translateY(-2px) scale(1.05)",
    background: `linear-gradient(135deg,
      #2563eb 0%,
      #1d4ed8 50%,
      #1e40af 100%
    )`,
    boxShadow: "0 12px 35px rgba(59, 130, 246, 0.6)",
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0) scale(0.98)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '10px 20px',
    fontSize: '0.8rem',
  },
}));

const Features = () => {
  const [hoveredIndex, setHoveredIndex] = useState(null);

  const sectionRef = useRef(null);
  const gridRef = useRef(null);
  const sectionInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const isInView = useInView(gridRef, { once: false, amount: 0.2 });

  // Enhanced animation variants
  const textVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275],
        staggerChildren: 0.1
      }
    },
  };

  // Stagger animation for grid items
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.9,
      rotateX: 15
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275]
      }
    },
  };

  return (
    <Section ref={sectionRef}>
      <Container maxWidth="xl">
        <motion.div
          initial="hidden"
          animate={sectionInView ? "visible" : "hidden"}
          variants={textVariants}
          style={{ textAlign: 'center', marginBottom: '40px' }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={sectionInView ? { opacity: 1, scale: 1, y: 0 } : {}}
            transition={{ duration: 1, ease: [0.175, 0.885, 0.32, 1.275] }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <AutoAwesome sx={{ fontSize: '2rem', color: '#3b82f6', mr: 2 }} />
              <Typography
                variant="h2"
                sx={{
                  fontWeight: '900',
                  background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                  fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                  letterSpacing: '-0.02em',
                }}
              >
                Our Core Services
              </Typography>
              <Computer sx={{ fontSize: '2rem', color: '#10b981', ml: 2 }} />
            </Box>
            <Typography
              variant="h5"
              sx={{
                maxWidth: 800,
                mx: 'auto',
                color: '#cbd5e1',
                fontWeight: '400',
                lineHeight: 1.6,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                opacity: 0.9
              }}
            >
              At Nelainey Consulting, we specialize in delivering expert financial,
              tax, audit, and digital transformation services tailored to the unique needs
              of organizations in Tanzania and around the globe. Our integrated solutions
              support sustainable growth, regulatory compliance, and operational excellence.
            </Typography>
          </motion.div>
        </motion.div>

        <motion.div
          ref={gridRef}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <FeaturesGrid>
            {FEATURES.map((feature, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                style={{
                  position: 'relative',
                  width: '100%',
                  willChange: 'transform, opacity'
                }}
              >
                <FeatureCard
                  style={{ backgroundImage: `url(${feature.img})` }}
                  whileHover={{
                    scale: 1.02,
                    transition: { duration: 0.3, ease: [0.175, 0.885, 0.32, 1.275] }
                  }}
                  onMouseEnter={() => setHoveredIndex(index)}
                  onMouseLeave={() => setHoveredIndex(null)}
                >
                <Overlay
                  style={
                    hoveredIndex === index
                      ? {
                          backdropFilter: "blur(3px)",
                          background: "rgba(0, 0, 0, 0.45)",
                        }
                      : {}
                  }
                />

                <CardContent>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <p
                      className="text-sm opacity-80"
                      style={{ fontFamily: "'Roboto', sans-serif" }}
                    >
                      {
                        [
                          "Financial Planning",
                          "Bookkeeping",
                          "Tax Optimization",
                          "Assurance & Tech",
                        ][index]
                      }
                    </p>
                    <h3
                      className="mt-4 text-2xl font-bold"
                      style={{ fontFamily: "'Montserrat', sans-serif" }}
                    >
                      {feature.title}
                    </h3>

                    {hoveredIndex === index && (
                      <motion.p
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3, duration: 0.6 }}
                        className="mt-4 text-sm font-light"
                        style={{ fontFamily: "'Roboto', sans-serif" }}
                      >
                        {feature.desc}
                      </motion.p>
                    )}

                    {hoveredIndex !== index && (
                      <ReadMoreButton>Read More</ReadMoreButton>
                    )}
                  </motion.div>
                </CardContent>
                </FeatureCard>
              </motion.div>
            ))}
          </FeaturesGrid>
        </motion.div>
      </Container>
    </Section>
  );
};

export default Features;
