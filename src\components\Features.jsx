import React, { useState } from "react";
import { Box, styled } from "@mui/system";
import { motion } from "framer-motion";
import { Typography } from "@mui/material";
import {
  GlobeEuropeAfricaIcon,
  MicrophoneIcon,
  PuzzlePieceIcon,
  HeartIcon,
} from "@heroicons/react/24/solid";

import img1 from "../assets/images/blog-1.jpg";
import img2 from "../assets/images/blog-2.jpg";
import img3 from "../assets/images/blog-3.jpg";
import img4 from "../assets/images/blog-4.jpg";
import img5 from "../assets/images/blog-5.jpg";

const FEATURES = [
  {
    img: img5,
    icon: HeartIcon,
    title: "Financial Services",
    desc:
      "We provide comprehensive financial planning, budgeting, and investment advice to ensure your business thrives financially, tailored to your specific needs across Tanzania and globally.",
  },
  {
    img: img2,
    icon: PuzzlePieceIcon,
    title: "Accounting Solutions",
    desc:
      "Our expert accounting services include bookkeeping, financial reporting, and audits, delivering accuracy and compliance to support your business growth and decision-making.",
  },
  {
    img: img3,
    icon: GlobeEuropeAfricaIcon,
    title: "Tax Consulting",
    desc:
      "We offer strategic tax planning and compliance services to optimize your tax obligations, ensuring you benefit from the latest regulations and minimize liabilities effectively.",
  },
  {
    img: img4,
    icon: MicrophoneIcon,
    title: "Assurance & ICT",
    desc:
      "Our assurance services guarantee reliability in financial statements, while our ICT solutions enhance your business operations with cutting-edge technology and cybersecurity measures.",
  },
];

const Section = styled(Box)(({ theme }) => ({
  marginTop: "80px",
  padding: "60px 16px 40px",
  background: "linear-gradient(135deg, #fafafa 0%, #c8c9d3	 100%)",
  position: "relative",
  overflow: "hidden",
  [theme.breakpoints.up("md")]: {
    marginTop: "100px",
    padding: "80px 32px 60px",
  },
}));

const ContentBox = styled(Box)(({ theme }) => ({
  maxWidth: "1300px",
  margin: "0 auto",
  textAlign: "center",
  paddingLeft: 16,
  paddingRight: 16,
}));

const FeatureCard = styled(motion.div)(({ theme }) => ({
  textAlign: "center",
  padding: "20px",
  borderRadius: "20px",
  overflow: "hidden",
  position: "relative",
  height: "clamp(300px, 25vw, 450px)",
  backgroundSize: "cover",
  backgroundPosition: "center",
  transition: "all 0.4s ease-in-out",
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  boxShadow: "0 8px 20px rgba(0, 0, 0, 0.12), 0 12px 40px rgba(0, 0, 0, 0.1)",
  [theme.breakpoints.down("sm")]: {
    height: "300px",
    padding: "12px",
  },
}));

const FeaturesGrid = styled(Box)(({ theme }) => ({
  display: "grid",
  gap: "24px",
  marginTop: "64px",
  gridTemplateColumns: "1fr",
  [theme.breakpoints.up("sm")]: {
    gridTemplateColumns: "repeat(2, 1fr)",
  },
  [theme.breakpoints.up("md")]: {
    gap: "32px",
  },
  [theme.breakpoints.up("lg")]: {
    gridTemplateColumns: "repeat(4, 1fr)",
  },
}));

const Overlay = styled("div")(() => ({
  position: "absolute",
  inset: 0,
  background: "rgba(0, 0, 0, 0.2)",
  backdropFilter: "blur(1.5px)",
  zIndex: 1,
  transition: "backdrop-filter 0.5s ease, background 0.5s ease",
}));

const CardContent = styled("div")(() => ({
  position: "relative",
  zIndex: 2,
  color: "#fff",
  textAlign: "center",
  padding: "20px",
  width: "100%",
}));

const ReadMoreButton = styled("button")(() => ({
  marginTop: "20px",
  padding: "10px 20px",
  fontSize: "14px",
  fontWeight: "bold",
  borderRadius: "30px",
  color: "#fff",
  background: "linear-gradient(to right, #1a237e, #2a3b8f)",
  border: "none",
  transition: "all 0.3s ease",
  cursor: "pointer",
  boxShadow: "0 4px 15px rgba(26, 35, 126, 0.4)",
  "&:hover": {
    transform: "scale(1.05)",
    background: "linear-gradient(to right, #0d1136, #1a237e)",
    boxShadow: "0 6px 20px rgba(50, 50, 150, 0.6)",
  },
}));

const Features = () => {
  const [hoveredIndex, setHoveredIndex] = useState(null);

  return (
    <Section>
      <ContentBox>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <Typography
            variant="h2"
            sx={{
              fontWeight: 800,
              fontSize: { xs: "28px", sm: "32px", md: "40px" },
              color: "#1a237e",
              fontFamily: "'Montserrat', sans-serif",
              mb: 4,
            }}
          >
            Our Core Services
          </Typography>
                  <Typography variant="h6" sx={{ maxWidth: 700, mx: 'auto', mb: 4, color: '#2a5298', fontStyle: 'italic' }}>
          
            At Nelainey Consulting, we specialize in delivering expert financial,
            tax, audit, and digital transformation services tailored to the unique needs
            of organizations in Tanzania and around the globe. Our integrated solutions
            support sustainable growth, regulatory compliance, and operational excellence
            across diverse industries. With a deep understanding of both regional
            dynamics and international standards, we are your strategic partner in
            navigating complexity and achieving measurable success.
          </Typography>
        </motion.div>

        <FeaturesGrid>
          {FEATURES.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: false, amount: 0.3 }}
              transition={{ duration: 0.7, delay: index * 0.2, ease: "easeOut" }}
            >
              <FeatureCard
                style={{ backgroundImage: `url(${feature.img})` }}
                whileHover={{ scale: 1.05 }}
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <Overlay
                  style={
                    hoveredIndex === index
                      ? {
                          backdropFilter: "blur(3px)",
                          background: "rgba(0, 0, 0, 0.45)",
                        }
                      : {}
                  }
                />

                <CardContent>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <p
                      className="text-sm opacity-80"
                      style={{ fontFamily: "'Roboto', sans-serif" }}
                    >
                      {
                        [
                          "Financial Planning",
                          "Bookkeeping",
                          "Tax Optimization",
                          "Assurance & Tech",
                        ][index]
                      }
                    </p>
                    <h3
                      className="mt-4 text-2xl font-bold"
                      style={{ fontFamily: "'Montserrat', sans-serif" }}
                    >
                      {feature.title}
                    </h3>

                    {hoveredIndex === index && (
                      <motion.p
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3, duration: 0.6 }}
                        className="mt-4 text-sm font-light"
                        style={{ fontFamily: "'Roboto', sans-serif" }}
                      >
                        {feature.desc}
                      </motion.p>
                    )}

                    {hoveredIndex !== index && (
                      <ReadMoreButton>Read More</ReadMoreButton>
                    )}
                  </motion.div>
                </CardContent>
              </FeatureCard>
            </motion.div>
          ))}
        </FeaturesGrid>
      </ContentBox>
    </Section>
  );
};

export default Features;
