import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Box, Typography, styled, useTheme, useMediaQuery, Container } from '@mui/material';
import { motion, AnimatePresence, useInView, useSpring, useTransform } from 'framer-motion';
import { ChevronRight, AutoAwesome, TrendingUp, Security } from '@mui/icons-material';

import img1 from '../assets/services/planning.jpg';
import img2 from '../assets/services/accounting.jpg';
import img3 from '../assets/services/tax.jpg';
import img4 from '../assets/services/assurance.jpg';
import img5 from '../assets/services/ict.jpg';
import img6 from '../assets/services/train.jpg';

const SERVICES = [
  {
    title: 'Financial Planning',
    tagline: 'Expertise • Ongoing Support • 500+ Clients',
    description: 'Comprehensive financial strategies to optimize your business finances, including budgeting and investment advice tailored to your goals.',
    image: img1,
  },
  {
    title: 'Accounting Services',
    tagline: 'Precision • Compliance • 300+ Audits',
    description: 'Accurate bookkeeping, financial reporting, and audit services to ensure compliance and support informed business decisions.',
    image: img2,
  },
  {
    title: 'Tax Consulting',
    tagline: 'Strategic • Regulatory • 400+ Cases',
    description: 'Expert tax planning and compliance to minimize liabilities and maximize benefits under the latest regulations across Tanzania and beyond.',
    image: img3,
  },
  {
    title: 'Assurance Services',
    tagline: 'Reliable • Tech-Driven • 250+ Projects',
    description: 'Guaranteed reliability in financial statements with advanced assurance techniques to build trust with stakeholders.',
    image: img4,
  },
  {
    title: 'ICT Solutions',
    tagline: 'Innovative • Secure • 150+ Solutions',
    description: 'Cutting-edge ICT services, including cybersecurity and technology integration, to enhance your business operations.',
    image: img5,
  },
  {
    title: 'Corporate Training',
    tagline: 'Training • Development • 100+ Programs',
    description: 'Tailored training programs to upskill your team in financial, tax, and consulting practices for long-term success.',
    image: img6,
  },
];

const Section = styled(Box)(({ theme }) => ({
  marginTop: '80px',
  padding: '80px 0',
  background: `
    linear-gradient(135deg,
      rgba(15, 23, 42, 0.97) 0%,
      rgba(30, 41, 59, 0.95) 25%,
      rgba(51, 65, 85, 0.93) 50%,
      rgba(71, 85, 105, 0.95) 75%,
      rgba(100, 116, 139, 0.97) 100%
    ),
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)
  `,
  position: 'relative',
  overflow: 'hidden',
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      repeating-linear-gradient(
        90deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      ),
      repeating-linear-gradient(
        0deg,
        transparent,
        transparent 98px,
        rgba(255, 255, 255, 0.03) 100px
      )
    `,
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('md')]: {
    padding: '60px 0',
    marginTop: '60px',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '40px 0',
    marginTop: '40px',
  },
}));

const Grid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: '32px',
  marginTop: '60px',
  padding: '0 20px',
  gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
  width: '100%',
  maxWidth: '1400px',
  marginLeft: 'auto',
  marginRight: 'auto',
  position: 'relative',
  justifyItems: 'center',
  [theme.breakpoints.down('lg')]: {
    gridTemplateColumns: 'repeat(auto-fit, minmax(320px, 1fr))',
    gap: '28px',
  },
  [theme.breakpoints.down('md')]: {
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: '24px',
    padding: '0 16px',
  },
  [theme.breakpoints.down('sm')]: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '20px',
    padding: '0 12px',
    overflow: 'visible',
  },
}));

const CardWrapper = styled(Box)(({ theme }) => ({
  perspective: '2000px',
  width: '100%',
  maxWidth: '380px',
  height: '580px',
  margin: '0 auto',
  position: 'relative',
  transformStyle: 'preserve-3d',
  [theme.breakpoints.down('lg')]: {
    maxWidth: '360px',
    height: '560px',
  },
  [theme.breakpoints.down('md')]: {
    maxWidth: '340px',
    height: '540px',
  },
  [theme.breakpoints.down('sm')]: {
    maxWidth: '320px',
    height: '520px',
    margin: '0 auto',
  },
}));

const Card = styled(motion.div)(({ theme }) => ({
  position: 'absolute',
  width: '100%',
  height: '100%',
  transformStyle: 'preserve-3d',
  transition: 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  cursor: 'pointer',
  borderRadius: '24px',
  background: `
    linear-gradient(145deg,
      rgba(255, 255, 255, 0.25) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0.05) 100%
    )
  `,
  border: '1px solid rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(20px)',
  WebkitBackdropFilter: 'blur(20px)',
  boxShadow: `
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,
  willChange: 'transform, box-shadow',
  '&:hover': {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: `
      0 35px 70px -12px rgba(0, 0, 0, 0.35),
      0 0 0 1px rgba(255, 255, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 50px rgba(59, 130, 246, 0.3)
    `,
  },
  '&:active': {
    transform: 'translateY(-4px) scale(0.98)',
  },
  [theme.breakpoints.down('sm')]: {
    borderRadius: '20px',
  },
}));

const CardFace = styled(Box)(({ back, theme }) => ({
  position: 'absolute',
  width: '100%',
  height: '100%',
  borderRadius: '24px',
  background: back
    ? `linear-gradient(145deg,
        rgba(15, 23, 42, 0.95) 0%,
        rgba(30, 41, 59, 0.9) 50%,
        rgba(51, 65, 85, 0.95) 100%
      )`
    : `linear-gradient(145deg,
        rgba(248, 250, 252, 0.95) 0%,
        rgba(241, 245, 249, 0.9) 50%,
        rgba(226, 232, 240, 0.95) 100%
      )`,
  border: back
    ? '1px solid rgba(59, 130, 246, 0.3)'
    : '1px solid rgba(255, 255, 255, 0.6)',
  color: back ? '#f8fafc' : '#1e293b',
  padding: '32px 24px',
  backfaceVisibility: 'hidden',
  transform: back ? 'rotateY(180deg)' : 'rotateY(0deg)',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'space-between',
  alignItems: 'center',
  textAlign: 'center',
  boxSizing: 'border-box',
  backdropFilter: 'blur(25px)',
  WebkitBackdropFilter: 'blur(25px)',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: back
      ? 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.1) 0%, transparent 50%)'
      : 'radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.05) 0%, transparent 50%)',
    pointerEvents: 'none',
  },
  [theme.breakpoints.down('sm')]: {
    borderRadius: '20px',
    padding: '28px 20px',
  },
}));

const Image = styled('img')(({ theme }) => ({
  width: '100%',
  height: '220px',
  objectFit: 'cover',
  borderRadius: '16px',
  marginBottom: '20px',
  objectPosition: 'center',
  transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  filter: 'brightness(1.1) contrast(1.1) saturate(1.2)',
  border: '2px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
  '&:hover': {
    transform: 'scale(1.02)',
    filter: 'brightness(1.2) contrast(1.2) saturate(1.3)',
    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.18)',
  },
  [theme.breakpoints.down('md')]: {
    height: '200px',
  },
  [theme.breakpoints.down('sm')]: {
    height: '180px',
    borderRadius: '12px',
  },
}));

const ReadMoreButton = styled(motion.button)(({ theme }) => ({
  marginTop: '20px',
  padding: '12px 28px',
  background: `linear-gradient(135deg,
    #3b82f6 0%,
    #2563eb 50%,
    #1d4ed8 100%
  )`,
  color: '#ffffff',
  border: 'none',
  borderRadius: '50px',
  cursor: 'pointer',
  fontWeight: '700',
  fontSize: '0.875rem',
  letterSpacing: '0.5px',
  textTransform: 'uppercase',
  transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  alignSelf: 'center',
  position: 'relative',
  overflow: 'hidden',
  boxShadow: '0 8px 25px rgba(59, 130, 246, 0.4)',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent)',
    transition: 'left 0.6s ease',
  },
  '&:hover': {
    background: `linear-gradient(135deg,
      #2563eb 0%,
      #1d4ed8 50%,
      #1e40af 100%
    )`,
    transform: 'translateY(-2px) scale(1.05)',
    boxShadow: '0 12px 35px rgba(59, 130, 246, 0.6)',
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(0) scale(0.98)',
  },
  [theme.breakpoints.down('sm')]: {
    padding: '10px 24px',
    fontSize: '0.8rem',
  },
}));

const ToggleButton = styled(motion.button)(({ theme }) => ({
  marginTop: '60px',
  padding: '16px 40px',
  background: `linear-gradient(135deg,
    rgba(59, 130, 246, 0.9) 0%,
    rgba(37, 99, 235, 0.9) 50%,
    rgba(29, 78, 216, 0.9) 100%
  )`,
  color: '#ffffff',
  border: '2px solid rgba(255, 255, 255, 0.2)',
  borderRadius: '50px',
  cursor: 'pointer',
  fontSize: '1.1rem',
  fontWeight: '700',
  letterSpacing: '0.5px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  gap: '12px',
  transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  backdropFilter: 'blur(20px)',
  WebkitBackdropFilter: 'blur(20px)',
  boxShadow: `
    0 20px 40px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2)
  `,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)',
    transition: 'left 0.6s ease',
  },
  '&:hover': {
    background: `linear-gradient(135deg,
      rgba(37, 99, 235, 0.95) 0%,
      rgba(29, 78, 216, 0.95) 50%,
      rgba(30, 64, 175, 0.95) 100%
    )`,
    transform: 'translateY(-4px) scale(1.05)',
    boxShadow: `
      0 25px 50px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3)
    `,
    border: '2px solid rgba(255, 255, 255, 0.3)',
    '&::before': {
      left: '100%',
    },
  },
  '&:active': {
    transform: 'translateY(-2px) scale(0.98)',
  },
  [theme.breakpoints.down('sm')]: {
    marginTop: '40px',
    padding: '14px 32px',
    fontSize: '1rem',
  },
}));

const OurServices = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [flippedIndex, setFlippedIndex] = useState(null);
  const [showMore, setShowMore] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.down('md'));

  const gridRef = useRef(null);
  const sectionRef = useRef(null);
  const isInView = useInView(gridRef, { once: false, amount: 0.2 });
  const sectionInView = useInView(sectionRef, { once: true, amount: 0.1 });

  // Smooth scroll spring animation
  const scrollY = useSpring(0, { stiffness: 100, damping: 30 });
  const y = useTransform(scrollY, [0, 1], [0, -50]);

  // Optimized auto-scroll with better performance
  useEffect(() => {
    let interval;
    if (!isPaused && !showMore && isMobile && isInView && !isHovered) {
      interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % SERVICES.slice(0, 4).length);
      }, 4000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isPaused, showMore, isMobile, isInView, isHovered]);

  // Optimized flip card handler
  const handleCardFlip = useCallback((index) => {
    setFlippedIndex(flippedIndex === index ? null : index);
    setIsPaused(flippedIndex !== index);
  }, [flippedIndex]);

  // Optimized show more handler
  const handleShowMore = useCallback(() => {
    setShowMore(prev => !prev);
    setFlippedIndex(null);
    setIsPaused(false);
  }, []);

  // Ultra-smooth animation variants
  const slideVariants = {
    enter: (index) => ({
      x: isMobile ? 300 : 0,
      y: isMobile ? 0 : 60,
      opacity: 0,
      scale: 0.9,
      rotateY: isMobile ? 0 : 15,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275],
        delay: isMobile ? 0 : index * 0.15
      },
    }),
    center: {
      x: 0,
      y: 0,
      opacity: 1,
      scale: 1,
      rotateY: 0,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275],
        delay: 0.1
      },
    },
    exit: {
      x: isMobile ? -300 : 0,
      y: isMobile ? 0 : -60,
      opacity: 0,
      scale: 0.9,
      rotateY: isMobile ? 0 : -15,
      transition: {
        duration: 0.6,
        ease: [0.175, 0.885, 0.32, 1.275]
      },
    },
  };

  // Enhanced text animations
  const textVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275],
        staggerChildren: 0.1
      }
    },
  };

  // Smooth card flip animations
  const cardVariants = {
    front: {
      rotateY: 0,
      transition: {
        duration: 0.6,
        ease: [0.175, 0.885, 0.32, 1.275]
      }
    },
    back: {
      rotateY: 180,
      transition: {
        duration: 0.6,
        ease: [0.175, 0.885, 0.32, 1.275]
      }
    },
  };

  // Stagger animation for grid items
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.9,
      rotateX: 15
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.8,
        ease: [0.175, 0.885, 0.32, 1.275]
      }
    },
  };

  const visibleServices = showMore ? SERVICES : SERVICES.slice(0, 4);

  return (
    <Section id="our-services" ref={sectionRef}>
      <Container maxWidth="xl">
        <motion.div
          initial="hidden"
          animate={sectionInView ? "visible" : "hidden"}
          variants={textVariants}
          style={{ textAlign: 'center', marginBottom: '40px' }}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={sectionInView ? { opacity: 1, scale: 1, y: 0 } : {}}
            transition={{ duration: 1, ease: [0.175, 0.885, 0.32, 1.275] }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
              <AutoAwesome sx={{ fontSize: '2rem', color: '#3b82f6', mr: 2 }} />
              <Typography
                variant="h2"
                sx={{
                  fontWeight: '900',
                  background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text',
                  textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                  fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                  letterSpacing: '-0.02em',
                }}
              >
                Our Premium Services
              </Typography>
              <TrendingUp sx={{ fontSize: '2rem', color: '#10b981', ml: 2 }} />
            </Box>
            <Typography
              variant="h5"
              sx={{
                maxWidth: 800,
                mx: 'auto',
                color: '#cbd5e1',
                fontWeight: '400',
                lineHeight: 1.6,
                fontSize: { xs: '1.1rem', md: '1.3rem' },
                opacity: 0.9
              }}
            >
              Experience world-class consulting solutions designed to elevate your business to unprecedented heights
            </Typography>
          </motion.div>
        </motion.div>

        <Box
          ref={gridRef}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <motion.div
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
          >
            <Grid>
              {!showMore && (
                <AnimatePresence initial={false} mode="wait">
                  {isInView && (isMobile ? (
                    <motion.div
                      key={currentIndex}
                      custom={0}
                      initial="enter"
                      animate="center"
                      exit="exit"
                      variants={slideVariants}
                      style={{
                        position: 'relative',
                        width: '100%',
                        willChange: 'transform, opacity'
                      }}
                    >
                      <CardWrapper>
                        <Card
                          variants={cardVariants}
                          animate={flippedIndex === currentIndex ? 'back' : 'front'}
                          whileHover={{
                            scale: 1.02,
                            transition: { duration: 0.3, ease: [0.175, 0.885, 0.32, 1.275] }
                          }}
                        >
                          <CardFace>
                            <Box sx={{ position: 'relative', zIndex: 1 }}>
                              <Image
                                src={visibleServices[currentIndex].image}
                                alt={visibleServices[currentIndex].title}
                                loading="lazy"
                              />
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, justifyContent: 'center' }}>
                                <Security sx={{ fontSize: '1rem', color: '#3b82f6', mr: 1 }} />
                                <Typography variant="caption" sx={{
                                  opacity: 0.8,
                                  fontSize: '0.85rem',
                                  fontWeight: '600',
                                  color: '#64748b',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px'
                                }}>
                                  {visibleServices[currentIndex].tagline}
                                </Typography>
                              </Box>
                              <Typography variant="h5" sx={{
                                fontWeight: '800',
                                mb: 2,
                                color: '#1e293b',
                                fontSize: { xs: '1.3rem', md: '1.5rem' },
                                lineHeight: 1.2
                              }}>
                                {visibleServices[currentIndex].title}
                              </Typography>
                              <Typography variant="body1" sx={{
                                fontSize: { xs: '0.9rem', md: '1rem' },
                                lineHeight: 1.6,
                                color: '#475569',
                                mb: 3
                              }}>
                                {visibleServices[currentIndex].description}
                              </Typography>
                            </Box>
                            <ReadMoreButton
                              onClick={() => handleCardFlip(currentIndex)}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              Explore More
                            </ReadMoreButton>
                          </CardFace>
                          <CardFace back>
                            <Box sx={{ position: 'relative', zIndex: 1, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                              <Box>
                                <Typography variant="h5" sx={{
                                  fontWeight: '800',
                                  mb: 3,
                                  color: '#f8fafc',
                                  textAlign: 'center'
                                }}>
                                  {visibleServices[currentIndex].title}
                                </Typography>
                                <Typography variant="body1" sx={{
                                  fontSize: { xs: '0.9rem', md: '1rem' },
                                  lineHeight: 1.7,
                                  color: '#cbd5e1',
                                  textAlign: 'center'
                                }}>
                                  {visibleServices[currentIndex].description}
                                  <br /><br />
                                  <strong style={{ color: '#3b82f6' }}>Ready to transform your business?</strong>
                                  <br />
                                  Contact our expert team for personalized solutions.
                                </Typography>
                              </Box>
                              <ReadMoreButton
                                onClick={() => handleCardFlip(null)}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                style={{
                                  background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%)',
                                  boxShadow: '0 8px 25px rgba(239, 68, 68, 0.4)'
                                }}
                              >
                                ← Back
                              </ReadMoreButton>
                            </Box>
                          </CardFace>
                        </Card>
                      </CardWrapper>
                </motion.div>
              ) : (
                    visibleServices.map((service, index) => (
                      <motion.div
                        key={index}
                        custom={index}
                        variants={itemVariants}
                        style={{
                          position: 'relative',
                          width: '100%',
                          willChange: 'transform, opacity'
                        }}
                      >
                        <CardWrapper>
                          <Card
                            variants={cardVariants}
                            animate={flippedIndex === index ? 'back' : 'front'}
                            whileHover={{
                              scale: 1.02,
                              transition: { duration: 0.3, ease: [0.175, 0.885, 0.32, 1.275] }
                            }}
                          >
                            <CardFace>
                              <Box sx={{ position: 'relative', zIndex: 1 }}>
                                <Image
                                  src={service.image}
                                  alt={service.title}
                                  loading="lazy"
                                />
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, justifyContent: 'center' }}>
                                  <Security sx={{ fontSize: '1rem', color: '#3b82f6', mr: 1 }} />
                                  <Typography variant="caption" sx={{
                                    opacity: 0.8,
                                    fontSize: '0.85rem',
                                    fontWeight: '600',
                                    color: '#64748b',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px'
                                  }}>
                                    {service.tagline}
                                  </Typography>
                                </Box>
                                <Typography variant="h5" sx={{
                                  fontWeight: '800',
                                  mb: 2,
                                  color: '#1e293b',
                                  fontSize: { xs: '1.3rem', md: '1.5rem' },
                                  lineHeight: 1.2
                                }}>
                                  {service.title}
                                </Typography>
                                <Typography variant="body1" sx={{
                                  fontSize: { xs: '0.9rem', md: '1rem' },
                                  lineHeight: 1.6,
                                  color: '#475569',
                                  mb: 3
                                }}>
                                  {service.description}
                                </Typography>
                              </Box>
                              <ReadMoreButton
                                onClick={() => handleCardFlip(index)}
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                              >
                                Explore More
                              </ReadMoreButton>
                            </CardFace>
                            <CardFace back>
                              <Box sx={{ position: 'relative', zIndex: 1, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                                <Box>
                                  <Typography variant="h5" sx={{
                                    fontWeight: '800',
                                    mb: 3,
                                    color: '#f8fafc',
                                    textAlign: 'center'
                                  }}>
                                    {service.title}
                                  </Typography>
                                  <Typography variant="body1" sx={{
                                    fontSize: { xs: '0.9rem', md: '1rem' },
                                    lineHeight: 1.7,
                                    color: '#cbd5e1',
                                    textAlign: 'center'
                                  }}>
                                    {service.description}
                                    <br /><br />
                                    <strong style={{ color: '#3b82f6' }}>Ready to transform your business?</strong>
                                    <br />
                                    Contact our expert team for personalized solutions.
                                  </Typography>
                                </Box>
                                <ReadMoreButton
                                  onClick={() => handleCardFlip(null)}
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  style={{
                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%)',
                                    boxShadow: '0 8px 25px rgba(239, 68, 68, 0.4)'
                                  }}
                                >
                                  ← Back
                                </ReadMoreButton>
                              </Box>
                            </CardFace>
                          </Card>
                        </CardWrapper>
                      </motion.div>
                    ))
                  ))}
                </AnimatePresence>
              )}
              {showMore && visibleServices.map((service, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true, amount: 0.3 }}
                  transition={{ duration: 0.8, delay: index * 0.1 }}
                >
                  <CardWrapper>
                    <Card
                      variants={cardVariants}
                      animate={flippedIndex === index ? 'back' : 'front'}
                      whileHover={{
                        scale: 1.02,
                        transition: { duration: 0.3, ease: [0.175, 0.885, 0.32, 1.275] }
                      }}
                    >
                      <CardFace>
                        <Box sx={{ position: 'relative', zIndex: 1 }}>
                          <Image
                            src={service.image}
                            alt={service.title}
                            loading="lazy"
                          />
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1, justifyContent: 'center' }}>
                            <Security sx={{ fontSize: '1rem', color: '#3b82f6', mr: 1 }} />
                            <Typography variant="caption" sx={{
                              opacity: 0.8,
                              fontSize: '0.85rem',
                              fontWeight: '600',
                              color: '#64748b',
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px'
                            }}>
                              {service.tagline}
                            </Typography>
                          </Box>
                          <Typography variant="h5" sx={{
                            fontWeight: '800',
                            mb: 2,
                            color: '#1e293b',
                            fontSize: { xs: '1.3rem', md: '1.5rem' },
                            lineHeight: 1.2
                          }}>
                            {service.title}
                          </Typography>
                          <Typography variant="body1" sx={{
                            fontSize: { xs: '0.9rem', md: '1rem' },
                            lineHeight: 1.6,
                            color: '#475569',
                            mb: 3
                          }}>
                            {service.description}
                          </Typography>
                        </Box>
                        <ReadMoreButton
                          onClick={() => handleCardFlip(index)}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          Explore More
                        </ReadMoreButton>
                      </CardFace>
                      <CardFace back>
                        <Box sx={{ position: 'relative', zIndex: 1, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="h5" sx={{
                              fontWeight: '800',
                              mb: 3,
                              color: '#f8fafc',
                              textAlign: 'center'
                            }}>
                              {service.title}
                            </Typography>
                            <Typography variant="body1" sx={{
                              fontSize: { xs: '0.9rem', md: '1rem' },
                              lineHeight: 1.7,
                              color: '#cbd5e1',
                              textAlign: 'center'
                            }}>
                              {service.description}
                              <br /><br />
                              <strong style={{ color: '#3b82f6' }}>Ready to transform your business?</strong>
                              <br />
                              Contact our expert team for personalized solutions.
                            </Typography>
                          </Box>
                          <ReadMoreButton
                            onClick={() => handleCardFlip(null)}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            style={{
                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%)',
                              boxShadow: '0 8px 25px rgba(239, 68, 68, 0.4)'
                            }}
                          >
                            ← Back
                          </ReadMoreButton>
                        </Box>
                      </CardFace>
                    </Card>
                  </CardWrapper>
                </motion.div>
              ))}
            </Grid>
          </motion.div>
        </Box>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.3 }}
          style={{ display: 'flex', justifyContent: 'center', marginTop: '60px' }}
        >
          <ToggleButton
            onClick={handleShowMore}
            whileHover={{
              scale: 1.05,
              transition: { duration: 0.3, ease: [0.175, 0.885, 0.32, 1.275] }
            }}
            whileTap={{ scale: 0.95 }}
          >
            {showMore ? (
              <>
                <ChevronRight sx={{ transform: 'rotate(90deg)', transition: 'transform 0.3s ease' }} />
                Show Essential Services
              </>
            ) : (
              <>
                Discover All Services
                <ChevronRight sx={{ transition: 'transform 0.3s ease' }} />
              </>
            )}
          </ToggleButton>
        </motion.div>
      </Container>
    </Section>
  );
};

export default OurServices;