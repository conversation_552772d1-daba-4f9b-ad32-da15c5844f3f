import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, styled, useTheme, useMediaQuery } from '@mui/material';
import { motion, AnimatePresence, useInView } from 'framer-motion';
import { ChevronRight } from '@mui/icons-material';

import img1 from '../assets/services/planning.jpg';
import img2 from '../assets/services/accounting.jpg';
import img3 from '../assets/services/tax.jpg';
import img4 from '../assets/services/assurance.jpg';
import img5 from '../assets/services/ict.jpg';
import img6 from '../assets/services/train.jpg';

const SERVICES = [
  {
    title: 'Financial Planning',
    tagline: 'Expertise • Ongoing Support • 500+ Clients',
    description: 'Comprehensive financial strategies to optimize your business finances, including budgeting and investment advice tailored to your goals.',
    image: img1,
  },
  {
    title: 'Accounting Services',
    tagline: 'Precision • Compliance • 300+ Audits',
    description: 'Accurate bookkeeping, financial reporting, and audit services to ensure compliance and support informed business decisions.',
    image: img2,
  },
  {
    title: 'Tax Consulting',
    tagline: 'Strategic • Regulatory • 400+ Cases',
    description: 'Expert tax planning and compliance to minimize liabilities and maximize benefits under the latest regulations across Tanzania and beyond.',
    image: img3,
  },
  {
    title: 'Assurance Services',
    tagline: 'Reliable • Tech-Driven • 250+ Projects',
    description: 'Guaranteed reliability in financial statements with advanced assurance techniques to build trust with stakeholders.',
    image: img4,
  },
  {
    title: 'ICT Solutions',
    tagline: 'Innovative • Secure • 150+ Solutions',
    description: 'Cutting-edge ICT services, including cybersecurity and technology integration, to enhance your business operations.',
    image: img5,
  },
  {
    title: 'Corporate Training',
    tagline: 'Training • Development • 100+ Programs',
    description: 'Tailored training programs to upskill your team in financial, tax, and consulting practices for long-term success.',
    image: img6,
  },
];

const Section = styled(Box)(({ theme }) => ({
  marginTop: '60px',
  padding: '40px 16px',
  background: 'linear-gradient(135deg, #fafafa 0%, #c8c9d3 100%)',
  textAlign: 'center',
  [theme.breakpoints.up('md')]: {
    padding: '60px 24px',
  },
}));

const Grid = styled(Box)(({ theme }) => ({
  display: 'grid',
  gap: '24px',
  marginTop: '32px',
  padding: '0 8px',
  gridTemplateColumns: 'repeat(4, 1fr)',
  width: '100%',
  maxWidth: '1600px',
  marginLeft: 'auto',
  marginRight: 'auto',
  position: 'relative',
  [theme.breakpoints.down('sm')]: {
    display: 'block',
    overflow: 'hidden',
    width: '100%',
    padding: 0,
  },
}));

const CardWrapper = styled(Box)(({ theme }) => ({
  perspective: '1500px',
  width: '100%',
  maxWidth: '320px',
  height: '540px',
  margin: '0 auto',
  position: 'relative',
  [theme.breakpoints.down('sm')]: {
    maxWidth: '100%',
    height: '520px',
    margin: 0,
  },
}));

const Card = styled(motion.div)(() => ({
  position: 'absolute',
  width: '100%',
  height: '100%',
  transformStyle: 'preserve-3d',
  transition: 'transform 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  borderRadius: '16px',
  boxShadow: '0 12px 24px rgba(0, 0, 0, 0.15), 0 8px 16px rgba(0, 0, 0, 0.1)',
  '&:hover': {
    boxShadow: '0 20px 40px rgba(0, 0, 0, 0.25), 0 12px 32px rgba(0, 0, 0, 0.18)',
    scale: 1.03,
  },
}));

const CardFace = styled(Box)(({ back }) => ({
  position: 'absolute',
  width: '100%',
  height: '100%',
  borderRadius: '16px',
  background: 'rgba(255, 255, 255, 0.20)',
  border: '1px solid rgba(255, 255, 255, 0.5)',
  boxShadow: '0 16px 48px rgba(31, 38, 135, 0.2)',
  color: '#1e3c72',
  padding: '20px',
  backfaceVisibility: 'hidden',
  transform: back ? 'rotateY(180deg)' : 'rotateY(0deg)',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  alignItems: 'center',
  textAlign: 'center',
  boxSizing: 'border-box',
  backdropFilter: 'blur(18px)',
  WebkitBackdropFilter: 'blur(18px)',
}));

const Image = styled('img')(({ theme }) => ({
  width: '100%',
  height: '260px',
  objectFit: 'cover',
  borderRadius: '12px',
  marginBottom: '18px',
  objectPosition: 'center',
  [theme.breakpoints.down('sm')]: {
    height: '240px',
  },
}));

const ReadMoreButton = styled('button')(() => ({
  marginTop: '16px',
  padding: '10px 20px',
  background: '#2563eb',
  color: '#fff',
  border: 'none',
  borderRadius: '9999px',
  cursor: 'pointer',
  fontWeight: '600',
  transition: 'all 0.3s ease',
  alignSelf: 'center',
  '&:hover': {
    background: '#1e40af',
    transform: 'scale(1.05)',
  },
}));

const ToggleButton = styled('button')(() => ({
  marginTop: '32px',
  padding: '10px 20px',
  background: '#2563eb',
  color: '#fff',
  border: 'none',
  borderRadius: '9999px',
  cursor: 'pointer',
  fontSize: '1rem',
  fontWeight: 600,
  display: 'flex',
  alignItems: 'center',
  gap: '8px',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: '#1e40af',
    transform: 'scale(1.05)',
  },
}));

const OurServices = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [flippedIndex, setFlippedIndex] = useState(null);
  const [showMore, setShowMore] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isPaused, setIsPaused] = useState(false);
  const gridRef = useRef(null);
  const isInView = useInView(gridRef, { once: false });

  useEffect(() => {
    let interval;
    if (!isPaused && !showMore && isMobile) {
      interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % (showMore ? SERVICES.length : 4));
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [isPaused, showMore, isMobile]);

  const slideVariants = {
    enter: (index) => ({
      x: isMobile ? 400 : (index === 0 ? -800 : index === 1 ? 800 : index === 2 ? -400 : 400),
      y: isMobile ? 0 : (index === 0 || index === 1 ? -400 : 400),
      opacity: 0,
      scale: 0.95,
      transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1], delay: isMobile ? 0 : index * 0.3 },
    }),
    center: {
      x: 0,
      y: 0,
      opacity: 1,
      scale: 1,
      transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1] },
    },
    exit: {
      x: isMobile ? -400 : 0,
      opacity: 0,
      scale: 0.95,
      transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1] },
    },
  };

  const textVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.7, ease: 'easeOut' } },
  };

  const cardVariants = {
    front: { rotateY: 0 },
    back: { rotateY: 180 },
  };

  const visibleServices = showMore ? SERVICES : SERVICES.slice(0, 4);

  return (
    <Section id="our-services">
      <motion.div
        initial={{ opacity: 0, y: 80 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <Typography variant="h3" sx={{ fontWeight: '900', mb: 3, color: '#1e3c72', textShadow: '2px 2px 4px rgba(0, 0, 0, 0.2)' }}>
          Explore Our Services
        </Typography>
        <Typography variant="h6" sx={{ maxWidth: 700, mx: 'auto', mb: 4, color: '#2a5298', fontStyle: 'italic' }}>
          Specialized solutions for growth and compliance.
        </Typography>
      </motion.div>

      <Box ref={gridRef}>
        <Grid>
          {!showMore && (
            <AnimatePresence initial={false} mode="wait">
              {isInView && (isMobile ? (
                <motion.div
                  key={currentIndex}
                  custom={0}
                  initial="enter"
                  animate="center"
                  exit="exit"
                  variants={slideVariants}
                  transition={{ duration: 0.9, ease: [0.25, 0.1, 0.25, 1] }}
                  style={{ position: 'relative', width: '100%' }}
                >
                  <CardWrapper>
                    <Card
                      variants={cardVariants}
                      animate={flippedIndex === currentIndex ? 'back' : 'front'}
                    >
                      <CardFace>
                        <Image src={visibleServices[currentIndex].image} alt={visibleServices[currentIndex].title} />
                        <Typography variant="subtitle2" sx={{ opacity: 0.7, fontSize: '0.9rem' }}>
                          {visibleServices[currentIndex].tagline}
                        </Typography>
                        <Typography variant="h6" sx={{ fontWeight: '700', mt: 1, color: '#2a5298' }}>
                          {visibleServices[currentIndex].title}
                        </Typography>
                        <Typography variant="body2" sx={{ mt: 1, fontSize: isMobile ? '0.85rem' : '0.95rem', minHeight: '60px' }}>
                          {visibleServices[currentIndex].description}
                        </Typography>
                        <ReadMoreButton onClick={() => { setFlippedIndex(currentIndex); setIsPaused(true); }}>
                          READ MORE
                        </ReadMoreButton>
                      </CardFace>
                      <CardFace back>
                        <Box>
                          <Typography variant="h6" sx={{ fontWeight: '700', mb: 2, color: '#2a5298' }}>
                            {visibleServices[currentIndex].title}
                          </Typography>
                          <Typography variant="body1" sx={{ fontSize: isMobile ? '0.85rem' : '0.95rem', mb: 2, color: '#1e3c72' }}>
                            {visibleServices[currentIndex].description}
                            <br /><br />
                            Contact us for tailored support.
                          </Typography>
                        </Box>
                        <ReadMoreButton onClick={() => { setFlippedIndex(null); setIsPaused(false); }}>
                          BACK
                        </ReadMoreButton>
                      </CardFace>
                    </Card>
                  </CardWrapper>
                </motion.div>
              ) : (
                visibleServices.map((service, index) => (
                  <motion.div
                    key={index}
                    custom={index}
                    initial="enter"
                    animate="center"
                    exit="exit"
                    variants={slideVariants}
                    transition={{ duration: 0.9, ease: [0.25, 0.1, 0.25, 1] }}
                    style={{ position: 'relative', width: '100%' }}
                  >
                    <CardWrapper>
                      <Card
                        variants={cardVariants}
                        animate={flippedIndex === index ? 'back' : 'front'}
                      >
                        <CardFace>
                          <Image src={service.image} alt={service.title} />
                          <Typography variant="subtitle2" sx={{ opacity: 0.7, fontSize: '0.9rem' }}>
                            {service.tagline}
                          </Typography>
                          <Typography variant="h6" sx={{ fontWeight: '700', mt: 1, color: '#2a5298' }}>
                            {service.title}
                          </Typography>
                          <Typography variant="body2" sx={{ mt: 1, fontSize: isMobile ? '0.85rem' : '0.95rem', minHeight: '60px' }}>
                            {service.description}
                          </Typography>
                          <ReadMoreButton onClick={() => { setFlippedIndex(index); setIsPaused(true); }}>
                            READ MORE
                          </ReadMoreButton>
                        </CardFace>
                        <CardFace back>
                          <Box>
                            <Typography variant="h6" sx={{ fontWeight: '700', mb: 2, color: '#2a5298' }}>
                              {service.title}
                            </Typography>
                            <Typography variant="body1" sx={{ fontSize: isMobile ? '0.85rem' : '0.95rem', mb: 2, color: '#1e3c72' }}>
                              {service.description}
                              <br /><br />
                              Contact us for tailored support.
                            </Typography>
                          </Box>
                          <ReadMoreButton onClick={() => { setFlippedIndex(null); setIsPaused(false); }}>
                            BACK
                          </ReadMoreButton>
                        </CardFace>
                      </Card>
                    </CardWrapper>
                  </motion.div>
                ))
              ))}
            </AnimatePresence>
          )}
          {showMore && visibleServices.map((service, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <CardWrapper>
                <Card
                  variants={cardVariants}
                  animate={flippedIndex === index ? 'back' : 'front'}
                >
                  <CardFace>
                    <Image src={service.image} alt={service.title} />
                    <Typography variant="subtitle2" sx={{ opacity: 0.7, fontSize: '0.9rem' }}>
                      {service.tagline}
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: '700', mt: 1, color: '#2a5298' }}>
                      {service.title}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1, fontSize: isMobile ? '0.85rem' : '0.95rem', minHeight: '60px' }}>
                      {service.description}
                    </Typography>
                    <ReadMoreButton onClick={() => { setFlippedIndex(index); setIsPaused(true); }}>
                      READ MORE
                    </ReadMoreButton>
                  </CardFace>
                  <CardFace back>
                    <Box>
                      <Typography variant="h6" sx={{ fontWeight: '700', mb: 2, color: '#2a5298' }}>
                        {service.title}
                      </Typography>
                      <Typography variant="body1" sx={{ fontSize: isMobile ? '0.85rem' : '0.95rem', mb: 2, color: '#1e3c72' }}>
                        {service.description}
                        <br /><br />
                        Contact us for tailored support.
                      </Typography>
                    </Box>
                    <ReadMoreButton onClick={() => { setFlippedIndex(null); setIsPaused(false); }}>
                      BACK
                    </ReadMoreButton>
                  </CardFace>
                </Card>
              </CardWrapper>
            </motion.div>
          ))}
        </Grid>
      </Box>

      <ToggleButton onClick={() => setShowMore(!showMore)}>
        {showMore ? 'Show Less' : 'More Services'} <ChevronRight />
      </ToggleButton>
    </Section>
  );
};

export default OurServices;