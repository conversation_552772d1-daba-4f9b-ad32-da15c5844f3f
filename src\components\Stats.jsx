import React, { useState, useEffect, useRef } from "react";
import { Box, Typography } from "@mui/material";
import { styled } from "@mui/system";
import PeopleIcon from "@mui/icons-material/People";
import DoneAllIcon from "@mui/icons-material/DoneAll";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import ScheduleIcon from "@mui/icons-material/Schedule";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme, useMediaQuery } from "@mui/material";

const STATS = [
  { icon: PeopleIcon, count: 5000, title: "Clients Served" },
  { icon: DoneAllIcon, count: 1200, title: "Projects Completed" },
  { icon: CalendarTodayIcon, count: 15, title: "Years of Experience" },
  { icon: ScheduleIcon, count: 24, title: "Support Hours Daily" },
];

// === CHANGE THESE COLORS TO YOUR PREFERRED ONES ===
const PRIMARY_COLOR = "#2a4d69"; // deep blue for icons and main title
const CARD_BG_COLOR = "linear-gradient(135deg, #e6f5ff 0%, #d1e8ff 100%)"; // gradient card background
const CARD_BORDER_COLOR = "rgba(150, 200, 255, 0.5)"; // enhanced border color
const MAIN_TITLE_COLOR = "#1b2a47"; // main section title
const TEXT_COLOR = "#333"; // description and card text color
const ACCENT_COLOR = "#ff6f61"; // attractive accent for hover

const Section = styled(Box)(({ theme }) => ({
  padding: "100px 20px 120px",
  background: "linear-gradient(135deg, #fafafa 0%, #c8c9d3 100%)",
  position: "relative",
  overflow: "hidden",
  [theme.breakpoints.up("sm")]: {
    padding: "110px 40px 130px",
  },
  [theme.breakpoints.up("md")]: {
    padding: "120px 60px 140px",
  },
  [theme.breakpoints.up("lg")]: {
    padding: "140px 80px 160px",
  },
}));

const ContentBox = styled(Box)(({ theme }) => ({
  maxWidth: 1100,
  margin: "0 auto",
  textAlign: "center",
  paddingLeft: 16,
  paddingRight: 16,
}));

const StatsGrid = styled(Box)(({ theme }) => ({
  display: "grid",
  gap: 24,
  marginTop: 64,
  gridTemplateColumns: "1fr",
  justifyContent: "center",
  [theme.breakpoints.up("sm")]: {
    gridTemplateColumns: "repeat(2, 1fr)",
    gap: 32,
  },
  [theme.breakpoints.up("lg")]: {
    gridTemplateColumns: "repeat(4, 1fr)",
    gap: 48,
  },
  [theme.breakpoints.down("sm")]: {
    display: "block",
    overflow: "hidden",
    width: "100%",
    padding: 0,
  },
}));

const StatsCard = styled(motion.div)(({ theme }) => ({
  textAlign: "center",
  borderRadius: 20,
  background: CARD_BG_COLOR,
  boxShadow: "0 8px 30px rgba(0, 0, 0, 0.05)",
  backdropFilter: "blur(10px)",
  WebkitBackdropFilter: "blur(10px)",
  border: `1px solid ${CARD_BORDER_COLOR}`,
  padding: theme.spacing(4, 3),
  transition: "transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease",
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  "&:hover": {
    transform: "translateY(-6px)",
    boxShadow: "0 12px 40px rgba(0, 0, 0, 0.1)",
    background: `linear-gradient(135deg, ${CARD_BG_COLOR}, ${ACCENT_COLOR}20)`,
  },
  [theme.breakpoints.down("sm")]: {
    width: "90%",
    maxWidth: 280,
    height: 200,
    margin: "0 auto 20px",
    padding: theme.spacing(2, 2),
  },
}));

const Stats = () => {
  const [displayCounts, setDisplayCounts] = useState(STATS.map(() => 0));
  const [currentIndex, setCurrentIndex] = useState(0);
  const animationFrame = useRef(null);
  const observerRef = useRef();
  const sectionRef = useRef();
  const hasAnimated = useRef(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [isPaused, setIsPaused] = useState(false);

  const animateCounts = () => {
    cancelAnimationFrame(animationFrame.current);
    const targets = STATS.map((stat) => stat.count);

    animationFrame.current = requestAnimationFrame(function step() {
      setDisplayCounts((current) => {
        let done = true;
        const next = current.map((val, idx) => {
          const target = targets[idx];
          if (val < target) {
            done = false;
            return Math.min(val + Math.ceil(target * 0.05), target);
          }
          return target;
        });
        if (!done) animationFrame.current = requestAnimationFrame(step);
        return next;
      });
    });
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated.current) {
          animateCounts();
          hasAnimated.current = true;
        }
      },
      { threshold: 0.4 }
    );

    if (sectionRef.current) observer.observe(sectionRef.current);
    observerRef.current = observer;

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    let interval;
    if (!isPaused && isMobile) {
      interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % STATS.length);
      }, 5000);
    }
    return () => clearInterval(interval);
  }, [isPaused, isMobile]);

  const slideVariants = {
    enter: { x: 400, opacity: 0, scale: 0.95 },
    center: { x: 0, opacity: 1, scale: 1, transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1] } },
    exit: { x: -400, opacity: 0, scale: 0.95, transition: { duration: 0.9, ease: [0.25, 0.1, 0.25, 1] } },
  };

  return (
    <Section ref={sectionRef} id="stats">
      <ContentBox>
        <Typography variant="h3" sx={{ fontWeight: '900', mb: 3, color: '#1e3c72', textShadow: '2px 2px 4px rgba(0, 0, 0, 0.2)' }}>
       
          Numbers That Reflect Our Impact
        </Typography>
        <Typography variant="h6" sx={{ maxWidth: 700, mx: 'auto', mb: 4, color: '#2a5298', fontStyle: 'italic' }}>
        
          At Nelainey Consulting, we transform numbers into milestones.
          From thousands of clients served to years of expertise—our track
          record speaks for itself. Here’s how we make a measurable difference.
        </Typography>

        <StatsGrid>
          {!isMobile ? (
            STATS.map((stat, idx) => (
              <StatsCard
                key={idx}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: idx * 0.2 }}
                viewport={{ once: true }}
              >
                <stat.icon
                  style={{
                    fontSize:
                      window.innerWidth < 600
                        ? 36
                        : window.innerWidth < 900
                        ? 48
                        : 60,
                    color: PRIMARY_COLOR,
                    marginBottom: 16,
                  }}
                />
                <Typography
                  component="span"
                  sx={{
                    fontWeight: 800,
                    fontSize: { xs: 28, sm: 36, md: 44, lg: 54 },
                    color: "#0d1136",
                    fontFamily: "'Montserrat', sans-serif",
                    fontVariantNumeric: "tabular-nums",
                    letterSpacing: "0.02em",
                    display: "block",
                  }}
                >
                  {displayCounts[idx].toLocaleString()}+
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    color: "#555",
                    fontWeight: 600,
                    fontSize: { xs: 13, sm: 15, md: 17 },
                    fontFamily: "'Roboto', sans-serif",
                    marginTop: 8,
                  }}
                >
                  {stat.title}
                </Typography>
              </StatsCard>
            ))
          ) : (
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                custom={0}
                initial="enter"
                animate="center"
                exit="exit"
                variants={slideVariants}
                transition={{ duration: 0.9, ease: [0.25, 0.1, 0.25, 1] }}
                style={{ position: "relative", width: "100%" }}
              >
                <StatsCard
                  onClick={() => setIsPaused(!isPaused)}
                  whileHover={{ scale: 1.05, transition: { duration: 0.3 } }}
                >
                  {React.createElement(STATS[currentIndex].icon, {
                    style: {
                      fontSize: 36,
                      color: PRIMARY_COLOR,
                      marginBottom: 12,
                    },
                  })}
                  <Typography
                    component="span"
                    sx={{
                      fontWeight: 800,
                      fontSize: 28,
                      color: "#0d1136",
                      fontFamily: "'Montserrat', sans-serif",
                      fontVariantNumeric: "tabular-nums",
                      letterSpacing: "0.02em",
                      display: "block",
                    }}
                  >
                    {displayCounts[currentIndex].toLocaleString()}+
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#555",
                      fontWeight: 600,
                      fontSize: 13,
                      fontFamily: "'Roboto', sans-serif",
                      marginTop: 6,
                    }}
                  >
                    {STATS[currentIndex].title}
                  </Typography>
                </StatsCard>
              </motion.div>
            </AnimatePresence>
          )}
        </StatsGrid>
      </ContentBox>
    </Section>
  );
};

export default Stats;